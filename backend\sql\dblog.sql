-- --------------------------------------------------------
-- 主机:                           172.16.100.18
-- 服务器版本:                        8.0.42-0ubuntu0.24.04.2 - (Ubuntu)
-- 服务器操作系统:                      Linux
-- HeidiSQL 版本:                  12.11.0.7065
-- --------------------------------------------------------

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET NAMES utf8 */;
/*!50503 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;


-- 导出 blong 的数据库结构
CREATE DATABASE IF NOT EXISTS `blong` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;
USE `blong`;

-- 导出  表 blong.categories 结构
CREATE TABLE IF NOT EXISTS `categories` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '分类描述',
  `sort` int DEFAULT '0' COMMENT '排序字段',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `idx_name` (`name`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- 正在导出表  blong.categories 的数据：~5 rows (大约)
INSERT INTO `categories` (`id`, `name`, `description`, `sort`, `status`, `create_time`, `update_time`) VALUES
	(1, '技术分享', '分享各种技术文章和教程', 1, 1, '2025-07-25 06:42:06', '2025-07-25 06:42:06'),
	(2, '生活随笔', '记录生活中的点点滴滴', 2, 1, '2025-07-25 06:42:06', '2025-07-25 06:42:06'),
	(3, '学习笔记', '学习过程中的心得体会', 3, 1, '2025-07-25 06:42:06', '2025-07-25 06:42:06'),
	(4, '项目经验', '项目开发中的经验总结', 4, 1, '2025-07-25 06:42:06', '2025-07-25 06:42:06'),
	(5, '工具推荐', '好用的开发工具和软件推荐', 5, 1, '2025-07-25 06:42:06', '2025-07-25 06:42:06');

-- 导出  表 blong.posts 结构
CREATE TABLE IF NOT EXISTS `posts` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '文章ID',
  `title` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章标题',
  `summary` text COLLATE utf8mb4_unicode_ci COMMENT '文章摘要',
  `content` longtext COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文章内容',
  `cover_image` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '封面图片URL',
  `author_id` bigint NOT NULL COMMENT '作者ID',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  `tags` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '标签，用逗号分隔',
  `view_count` int DEFAULT '0' COMMENT '浏览次数',
  `like_count` int DEFAULT '0' COMMENT '点赞次数',
  `comment_count` int DEFAULT '0' COMMENT '评论次数',
  `status` int NOT NULL DEFAULT '0' COMMENT '状态：0-草稿 1-待审核 2-已发布 3-审核拒绝 4-已删除',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
  `reviewer_id` bigint DEFAULT NULL COMMENT '审核人ID',
  `review_time` datetime DEFAULT NULL COMMENT '审核时间',
  `review_comment` text COLLATE utf8mb4_unicode_ci COMMENT '审核意见',
  PRIMARY KEY (`id`),
  KEY `idx_title` (`title`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_publish_time` (`publish_time`),
  CONSTRAINT `posts_ibfk_1` FOREIGN KEY (`author_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `posts_ibfk_2` FOREIGN KEY (`category_id`) REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';

-- 正在导出表  blong.posts 的数据：~3 rows (大约)
INSERT INTO `posts` (`id`, `title`, `summary`, `content`, `cover_image`, `author_id`, `category_id`, `tags`, `view_count`, `like_count`, `comment_count`, `status`, `create_time`, `update_time`, `publish_time`, `reviewer_id`, `review_time`, `review_comment`) VALUES
	(1, 'Spring Boot 入门指南', 'Spring Boot 是一个快速开发框架，本文介绍如何快速上手。', '# Spring Boot 入门指南\n\nSpring Boot 是由 Pivotal 团队提供的全新框架，其设计目的是用来简化新 Spring 应用的初始搭建以及开发过程。\n\n## 主要特性\n\n1. 创建独立的 Spring 应用程序\n2. 嵌入的 Tomcat，无需部署 WAR 文件\n3. 简化 Maven 配置\n4. 自动配置 Spring\n5. 提供生产就绪型功能\n\n## 快速开始\n\n### 1. 创建项目\n\n使用 Spring Initializr 创建项目...\n\n### 2. 添加依赖\n\n```xml\n<dependency>\n    <groupId>org.springframework.boot</groupId>\n    <artifactId>spring-boot-starter-web</artifactId>\n</dependency>\n```\n\n### 3. 编写代码\n\n创建一个简单的控制器...\n\n这就是 Spring Boot 的基本使用方法，是不是很简单？', NULL, 1, 1, 'Spring Boot,Java,后端开发', 188, 25, 20, 2, '2025-07-25 06:42:06', '2025-07-28 08:56:43', '2025-07-28 13:45:44', NULL, NULL, NULL),
	(3, '我的编程学习之路', '分享我从零基础到成为程序员的学习经历和心得体会。', '# 我的编程学习之路\n\n回想起刚开始学习编程的时候，那种既兴奋又迷茫的心情至今还记得很清楚。\n\n## 初学阶段\n\n最开始接触的是 HTML 和 CSS，那时候觉得能做出一个简单的网页就已经很了不起了...\n\n## 深入学习\n\n随着学习的深入，开始接触 JavaScript、Java 等编程语言...\n\n## 项目实践\n\n理论学习固然重要，但实践才是检验真理的唯一标准...\n\n## 持续成长\n\n编程是一个需要持续学习的领域，技术更新很快...\n\n希望我的经历能对正在学习编程的朋友们有所帮助！', NULL, 1, 2, '学习经历,编程,成长', 64, 39, 1, 2, '2025-07-25 06:42:06', '2025-07-28 08:29:23', '2025-07-28 14:10:31', NULL, NULL, NULL),
	(5, '推荐几个好用的开发工具', '分享一些我在开发过程中经常使用的高效工具。', '# 推荐几个好用的开发工具\n\n工欲善其事，必先利其器。好的开发工具可以大大提高我们的工作效率。\n\n## 代码编辑器\n\n### 1. Visual Studio Code\n\n微软开发的免费代码编辑器，功能强大，插件丰富...\n\n### 2. IntelliJ IDEA\n\nJetBrains 出品的 Java 开发神器...\n\n## 版本控制\n\n### Git\n\n分布式版本控制系统，现代开发必备...\n\n## 数据库工具\n\n### Navicat\n\n强大的数据库管理工具...\n\n### DBeaver\n\n免费的通用数据库工具...\n\n## API 测试\n\n### Postman\n\nAPI 开发和测试的利器...\n\n这些工具都是我在日常开发中经常使用的，推荐给大家！', NULL, 1, 5, '开发工具,效率,推荐', 322, 14, 15, 2, '2025-07-25 06:42:06', '2025-07-28 08:56:19', '2025-07-28 14:10:37', NULL, NULL, NULL);

-- 导出  表 blong.site_info 结构
CREATE TABLE IF NOT EXISTS `site_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `site_name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '知识分享平台' COMMENT '网站名称',
  `site_description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '探索技术前沿 · 分享深度思考 · 记录成长足迹' COMMENT '网站描述',
  `icp_record` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT 'ICP备案号',
  `police_record` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '公安备案号',
  `copyright` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT '© 2025 知识分享平台. All rights reserved.' COMMENT '版权信息',
  `contact_email` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系邮箱',
  `contact_phone` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系电话',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='网站信息表';

-- 正在导出表  blong.site_info 的数据：~1 rows (大约)
INSERT INTO `site_info` (`id`, `site_name`, `site_description`, `icp_record`, `police_record`, `copyright`, `contact_email`, `contact_phone`, `create_time`, `update_time`) VALUES
	(1, '知识分享平台', '探索技术前沿 · 分享深度思考 · 记录成长足迹', '京ICP备12345678号-1', '京公网安备11010802012345号', '© 2025 知识分享平台. All rights reserved.', '<EMAIL>', '17312310502', '2025-07-28 07:47:25', '2025-07-28 08:16:29');

-- 导出  表 blong.users 结构
CREATE TABLE IF NOT EXISTS `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码',
  `email` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '邮箱',
  `nickname` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '头像URL',
  `bio` text COLLATE utf8mb4_unicode_ci COMMENT '个人简介',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `role` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT 'user' COMMENT '用户角色：admin-管理员，user-普通用户',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`),
  KEY `idx_username` (`username`),
  KEY `idx_email` (`email`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_users_role` (`role`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 正在导出表  blong.users 的数据：~2 rows (大约)
INSERT INTO `users` (`id`, `username`, `password`, `email`, `nickname`, `avatar`, `bio`, `status`, `create_time`, `update_time`, `role`) VALUES
	(1, 'admin', '$2a$10$KbHL016xKkB7oBhdy865WODCUSNqYXYVvLjRpYJbxiiUIWwLZetBi', '<EMAIL>', '管理员', NULL, '系统管理员账号', 1, '2025-07-25 06:42:06', '2025-07-28 08:23:51', 'admin'),
	(4, 'xiaoming', '$2a$10$KbHL016xKkB7oBhdy865WODCUSNqYXYVvLjRpYJbxiiUIWwLZetBi', '<EMAIL>', 'xiaoming', NULL, NULL, 1, '2025-07-25 14:44:57', '2025-07-25 14:44:57', 'user');

/*!40103 SET TIME_ZONE=IFNULL(@OLD_TIME_ZONE, 'system') */;
/*!40101 SET SQL_MODE=IFNULL(@OLD_SQL_MODE, '') */;
/*!40014 SET FOREIGN_KEY_CHECKS=IFNULL(@OLD_FOREIGN_KEY_CHECKS, 1) */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40111 SET SQL_NOTES=IFNULL(@OLD_SQL_NOTES, 1) */;
