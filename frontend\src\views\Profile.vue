<template>
  <div class="profile-page">
    <div class="profile-container">
      <el-card class="profile-card">
        <template #header>
          <div class="card-header">
            <h2>个人资料</h2>
          </div>
        </template>

        <div class="profile-content">
          <!-- 头像区域 -->
          <div class="avatar-section">
            <el-avatar :size="120" :src="form.avatar" class="profile-avatar">
              <template #default>
                <el-icon size="60"><User /></el-icon>
              </template>
            </el-avatar>
            <div class="avatar-actions">
              <el-upload
                :show-file-list="false"
                :before-upload="beforeAvatarUpload"
                :on-success="handleAvatarSuccess"
                action="/api/upload/avatar"
                accept="image/*"
              >
                <el-button type="primary" size="small">
                  <el-icon><Upload /></el-icon>
                  更换头像
                </el-button>
              </el-upload>
            </div>
          </div>

          <!-- 表单区域 -->
          <div class="form-section">
            <el-form
              ref="profileForm"
              :model="form"
              :rules="rules"
              label-width="100px"
              label-position="left"
            >
              <el-form-item label="用户名" prop="username">
                <el-input v-model="form.username" disabled />
              </el-form-item>

              <el-form-item label="昵称" prop="nickname">
                <el-input v-model="form.nickname" placeholder="请输入昵称" />
              </el-form-item>

              <el-form-item label="邮箱" prop="email">
                <el-input v-model="form.email" placeholder="请输入邮箱" />
              </el-form-item>

              <el-form-item label="个人简介" prop="bio">
                <el-input
                  v-model="form.bio"
                  type="textarea"
                  :rows="4"
                  placeholder="请输入个人简介"
                  maxlength="200"
                  show-word-limit
                />
              </el-form-item>

              <el-form-item label="角色">
                <el-tag :type="form.role === 'admin' ? 'danger' : 'primary'">
                  {{ form.role === 'admin' ? '管理员' : '普通用户' }}
                </el-tag>
              </el-form-item>

              <el-form-item label="注册时间">
                <span class="info-text">{{ formatDate(form.createTime) }}</span>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="updateProfile" :loading="loading">
                  保存修改
                </el-button>
                <el-button @click="resetForm">重置</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </el-card>

      <!-- 修改密码卡片 -->
      <el-card class="password-card">
        <template #header>
          <div class="card-header">
            <h3>修改密码</h3>
          </div>
        </template>

        <el-form
          ref="passwordForm"
          :model="passwordForm"
          :rules="passwordRules"
          label-width="100px"
        >
          <el-form-item label="当前密码" prop="currentPassword">
            <el-input
              v-model="passwordForm.currentPassword"
              type="password"
              placeholder="请输入当前密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="新密码" prop="newPassword">
            <el-input
              v-model="passwordForm.newPassword"
              type="password"
              placeholder="请输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item label="确认密码" prop="confirmPassword">
            <el-input
              v-model="passwordForm.confirmPassword"
              type="password"
              placeholder="请再次输入新密码"
              show-password
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="changePassword" :loading="passwordLoading">
              修改密码
            </el-button>
            <el-button @click="resetPasswordForm">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import { userStore } from '../store/user'
import { ElMessage } from 'element-plus'

export default {
  name: 'Profile',
  data() {
    return {
      userStore,
      loading: false,
      passwordLoading: false,
      form: {
        username: '',
        nickname: '',
        email: '',
        bio: '',
        avatar: '',
        role: '',
        createTime: ''
      },
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },
      rules: {
        nickname: [
          { required: true, message: '请输入昵称', trigger: 'blur' },
          { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ]
      },
      passwordRules: {
        currentPassword: [
          { required: true, message: '请输入当前密码', trigger: 'blur' }
        ],
        newPassword: [
          { required: true, message: '请输入新密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: '请再次输入新密码', trigger: 'blur' },
          { validator: this.validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadUserProfile()
  },
  methods: {
    loadUserProfile() {
      if (userStore.user) {
        this.form = { ...userStore.user }
      }
    },
    
    validateConfirmPassword(rule, value, callback) {
      if (value !== this.passwordForm.newPassword) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    },

    async updateProfile() {
      try {
        await this.$refs.profileForm.validate()
        this.loading = true
        
        const response = await fetch(`/api/users/${userStore.user.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            nickname: this.form.nickname,
            email: this.form.email,
            bio: this.form.bio,
            avatar: this.form.avatar
          })
        })

        const result = await response.json()
        if (result.code === 200) {
          userStore.updateUser(this.form)
          ElMessage.success('个人资料更新成功')
        } else {
          ElMessage.error(result.message || '更新失败')
        }
      } catch (error) {
        console.error('更新个人资料失败:', error)
        ElMessage.error('更新失败')
      } finally {
        this.loading = false
      }
    },

    async changePassword() {
      try {
        await this.$refs.passwordForm.validate()
        this.passwordLoading = true

        const requestData = {
          userId: userStore.user.id,
          currentPassword: this.passwordForm.currentPassword,
          newPassword: this.passwordForm.newPassword,
          confirmPassword: this.passwordForm.confirmPassword
        }

        const response = await fetch('/api/users/change-password', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestData)
        })

        const result = await response.json()
        if (result.code === 200) {
          ElMessage.success('密码修改成功')
          this.resetPasswordForm()
        } else {
          ElMessage.error(result.message || '密码修改失败')
        }
      } catch (error) {
        console.error('修改密码失败:', error)
        ElMessage.error('修改密码失败')
      } finally {
        this.passwordLoading = false
      }
    },

    resetForm() {
      this.loadUserProfile()
    },

    resetPasswordForm() {
      this.passwordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.$refs.passwordForm?.clearValidate()
    },

    beforeAvatarUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过 2MB!')
        return false
      }
      return true
    },

    handleAvatarSuccess(response) {
      if (response.code === 200) {
        this.form.avatar = response.data
        ElMessage.success('头像上传成功')
      } else {
        ElMessage.error('头像上传失败')
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.profile-page {
  min-height: calc(100vh - 120px);
  background: #f5f7fa;
  padding: 20px;
}

.profile-container {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.profile-card,
.password-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.card-header h2,
.card-header h3 {
  margin: 0;
  color: #303133;
}

.profile-content {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  flex-shrink: 0;
}

.profile-avatar {
  border: 3px solid #e4e7ed;
  transition: all 0.3s ease;
}

.profile-avatar:hover {
  border-color: #409eff;
}

.form-section {
  flex: 1;
  min-width: 0;
}

.info-text {
  color: #909399;
  font-size: 14px;
}

@media (max-width: 768px) {
  .profile-content {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }
  
  .form-section {
    width: 100%;
  }
}
</style>
