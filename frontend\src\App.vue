<template>
  <div id="app">
    <el-container>
      <!-- 头部导航 -->
      <el-header class="header" v-if="!$route.meta.hideNavigation">
        <div class="header-content">
          <div class="logo">
            <h2>Spring Boot Blog</h2>
          </div>
          <div class="nav-section">
            <el-menu
              mode="horizontal"
              :default-active="$route.path"
              router
              class="nav-menu"
            >
              <el-menu-item index="/">首页</el-menu-item>
              <el-menu-item index="/posts">文章</el-menu-item>
              <el-menu-item index="/categories">分类</el-menu-item>
              <el-menu-item index="/about">关于</el-menu-item>
            </el-menu>

            <!-- 用户菜单 -->
            <div class="user-menu">
              <template v-if="userStore.isLoggedIn">
                <el-dropdown @command="handleUserCommand" trigger="click">
                  <div class="user-info">
                    <el-avatar
                      :size="32"
                      :src="userStore.user.avatar"
                      class="user-avatar"
                    >
                      <template #default>
                        <el-icon><User /></el-icon>
                      </template>
                    </el-avatar>
                    <div class="user-details">
                      <span class="username">{{ userStore.user.nickname || userStore.user.username }}</span>
                      <span class="user-role" v-if="userStore.isAdmin()">管理员</span>
                    </div>
                    <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
                  </div>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="write" icon="Edit">
                        <el-icon><Edit /></el-icon>
                        写文章
                      </el-dropdown-item>
                      <el-dropdown-item command="manage" icon="Document">
                        <el-icon><Document /></el-icon>
                        我的文章
                      </el-dropdown-item>
                      <el-dropdown-item command="review" v-if="userStore.hasReviewPermission()" icon="Check">
                        <el-icon><Check /></el-icon>
                        审核管理
                      </el-dropdown-item>
                      <el-dropdown-item command="categories" v-if="userStore.isAdmin()" icon="Collection">
                        <el-icon><Collection /></el-icon>
                        分类管理
                      </el-dropdown-item>
                      <el-dropdown-item command="users" v-if="userStore.isAdmin()" icon="UserFilled">
                        <el-icon><UserFilled /></el-icon>
                        用户管理
                      </el-dropdown-item>
                      <el-dropdown-item command="profile" divided icon="User">
                        <el-icon><User /></el-icon>
                        个人资料
                      </el-dropdown-item>
                      <el-dropdown-item command="settings" icon="Setting">
                        <el-icon><Setting /></el-icon>
                        设置
                      </el-dropdown-item>
                      <el-dropdown-item command="logout" divided icon="SwitchButton">
                        <el-icon><SwitchButton /></el-icon>
                        退出登录
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </template>
              <template v-else>
                <el-button link @click="$router.push('/login')">登录</el-button>
                <el-button type="primary" @click="$router.push('/register')">注册</el-button>
              </template>
            </div>
          </div>
        </div>
      </el-header>

      <!-- 主要内容区域 -->
      <el-main class="main-content" :class="{ 'full-height': $route.meta.hideNavigation }">
        <router-view />
      </el-main>

      <!-- 底部 -->
      <el-footer class="footer" v-if="!$route.meta.hideNavigation">
        <div class="footer-content">
          <p>&copy; 2025 Spring Boot Blog. All rights reserved.</p>
        </div>
      </el-footer>
    </el-container>
  </div>
</template>

<script>
import { userStore } from './store/user'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'App',
  data() {
    return {
      userStore
    }
  },
  mounted() {
    // 初始化用户状态
    userStore.init()
  },
  methods: {
    async handleUserCommand(command) {
      switch (command) {
        case 'write':
          this.$router.push('/editor')
          break
        case 'manage':
          this.$router.push('/manage')
          break
        case 'review':
          this.$router.push('/review')
          break
        case 'categories':
          this.$router.push('/admin/categories')
          break
        case 'users':
          this.$router.push('/admin/users')
          break
        case 'profile':
          this.$router.push('/profile')
          break
        case 'settings':
          this.$message.info('设置功能开发中...')
          break
        case 'logout':
          try {
            await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            })
            userStore.logout()
            ElMessage.success('已退出登录')
            if (this.$route.meta.requiresAuth) {
              this.$router.push('/login')
            }
          } catch {
            // 用户取消
          }
          break
      }
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#app {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  min-height: 100vh;
}

.header {
  background-color: #fff;
  border-bottom: 1px solid #e6e6e6;
  padding: 0;
}

.header-content {
  max-width: 1800px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 30px;
}

.logo h2 {
  color: #409eff;
  margin: 0;
}

.nav-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-menu {
  border-bottom: none;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: #606266;
  font-size: 14px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.user-info:hover {
  background-color: #f5f7fa;
  color: #409eff;
}

.user-avatar {
  flex-shrink: 0;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.username {
  font-weight: 500;
  color: #303133;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.user-role {
  font-size: 12px;
  color: #409eff;
  background: #ecf5ff;
  padding: 1px 6px;
  border-radius: 10px;
  margin-top: 2px;
}

.dropdown-icon {
  flex-shrink: 0;
  transition: transform 0.3s ease;
}

.user-info:hover .dropdown-icon {
  transform: rotate(180deg);
}

.main-content {
  max-width: 1800px;
  margin: 0 auto;
  padding: 30px;
  min-height: calc(100vh - 120px);
}

.main-content.full-height {
  max-width: none;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.footer {
  background-color: #f5f5f5;
  border-top: 1px solid #e6e6e6;
  text-align: center;
  padding: 20px 0;
}

.footer-content {
  max-width: 1800px;
  margin: 0 auto;
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
    flex-wrap: wrap;
    gap: 10px;
  }

  .logo h2 {
    font-size: 1.3rem;
  }

  .nav-section {
    gap: 15px;
  }

  .nav-menu .el-menu-item {
    padding: 0 10px;
    font-size: 0.9rem;
  }

  .user-menu {
    gap: 8px;
  }

  .user-info {
    font-size: 0.85rem;
  }

  .main-content {
    padding: 15px;
  }

  .footer {
    padding: 15px 0;
  }

  .footer-content {
    padding: 0 15px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 10px;
    height: auto;
    min-height: 60px;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    gap: 8px;
  }

  .logo {
    width: 100%;
    text-align: center;
  }

  .logo h2 {
    font-size: 1.2rem;
  }

  .nav-section {
    width: 100%;
    justify-content: space-between;
    gap: 10px;
  }

  .nav-menu {
    flex: 1;
  }

  .nav-menu .el-menu-item {
    padding: 0 8px;
    font-size: 0.85rem;
  }

  .user-menu {
    gap: 6px;
  }

  .user-info {
    font-size: 0.8rem;
  }

  .main-content {
    padding: 10px;
    min-height: calc(100vh - 140px);
  }

  .footer {
    padding: 12px 0;
  }

  .footer-content {
    padding: 0 10px;
    font-size: 0.85rem;
  }
}
</style>
