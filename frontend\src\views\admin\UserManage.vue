<template>
  <div class="user-manage-page">
    <div class="page-header">
      <h2>用户管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        添加用户
      </el-button>
    </div>

    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索用户名或邮箱"
        style="width: 300px"
        @keyup.enter="loadUsers"
      >
        <template #append>
          <el-button @click="loadUsers">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 用户表格 -->
    <el-table :data="users" v-loading="loading" stripe>
      <el-table-column prop="id" label="ID" width="80" />
      
      <el-table-column label="头像" width="80">
        <template #default="{ row }">
          <el-avatar :size="40" :src="row.avatar">
            <template #default>
              <el-icon><User /></el-icon>
            </template>
          </el-avatar>
        </template>
      </el-table-column>

      <el-table-column prop="username" label="用户名" />
      <el-table-column prop="nickname" label="昵称" />
      <el-table-column prop="email" label="邮箱" />
      
      <el-table-column label="角色" width="100">
        <template #default="{ row }">
          <el-tag :type="row.role === 'admin' ? 'danger' : 'primary'">
            {{ row.role === 'admin' ? '管理员' : '普通用户' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="状态" width="100">
        <template #default="{ row }">
          <el-switch
            v-model="row.status"
            :active-value="1"
            :inactive-value="0"
            @change="updateUserStatus(row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="注册时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editUser(row)">编辑</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="deleteUser(row)"
            :disabled="row.id === userStore.user.id"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="loadUsers"
        @current-change="loadUsers"
      />
    </div>

    <!-- 创建/编辑用户对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑用户' : '添加用户'"
      width="500px"
    >
      <el-form
        ref="userForm"
        :model="userForm"
        :rules="userRules"
        label-width="80px"
      >
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userForm.username" :disabled="isEdit" />
        </el-form-item>

        <el-form-item label="昵称" prop="nickname">
          <el-input v-model="userForm.nickname" />
        </el-form-item>

        <el-form-item label="邮箱" prop="email">
          <el-input v-model="userForm.email" />
        </el-form-item>

        <el-form-item label="密码" prop="password" v-if="!isEdit">
          <el-input v-model="userForm.password" type="password" />
        </el-form-item>

        <el-form-item label="角色" prop="role">
          <el-select v-model="userForm.role" style="width: 100%">
            <el-option label="普通用户" value="user" />
            <el-option label="管理员" value="admin" />
          </el-select>
        </el-form-item>

        <el-form-item label="个人简介" prop="bio">
          <el-input
            v-model="userForm.bio"
            type="textarea"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveUser" :loading="saving">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { userStore } from '../../store/user'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'UserManage',
  data() {
    return {
      userStore,
      loading: false,
      saving: false,
      users: [],
      searchKeyword: '',
      currentPage: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      isEdit: false,
      userForm: {
        username: '',
        nickname: '',
        email: '',
        password: '',
        role: 'user',
        bio: ''
      },
      userRules: {
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ],
        nickname: [
          { required: true, message: '请输入昵称', trigger: 'blur' }
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadUsers()
  },
  methods: {
    async loadUsers() {
      try {
        this.loading = true
        const response = await fetch(`/api/users/page?pageNum=${this.currentPage}&pageSize=${this.pageSize}`)
        const result = await response.json()
        
        if (result.code === 200) {
          this.users = result.data.list
          this.total = result.data.total
        } else {
          ElMessage.error(result.message || '加载用户列表失败')
        }
      } catch (error) {
        console.error('加载用户列表失败:', error)
        ElMessage.error('加载用户列表失败')
      } finally {
        this.loading = false
      }
    },

    showCreateDialog() {
      this.isEdit = false
      this.userForm = {
        username: '',
        nickname: '',
        email: '',
        password: '',
        role: 'user',
        bio: ''
      }
      this.dialogVisible = true
    },

    editUser(user) {
      this.isEdit = true
      this.userForm = { ...user }
      this.dialogVisible = true
    },

    async saveUser() {
      try {
        await this.$refs.userForm.validate()
        this.saving = true

        const url = this.isEdit ? `/api/users/${this.userForm.id}` : '/api/users'
        const method = this.isEdit ? 'PUT' : 'POST'

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.userForm)
        })

        const result = await response.json()
        if (result.code === 200) {
          ElMessage.success(this.isEdit ? '用户更新成功' : '用户创建成功')
          this.dialogVisible = false
          this.loadUsers()
        } else {
          ElMessage.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('保存用户失败:', error)
        ElMessage.error('操作失败')
      } finally {
        this.saving = false
      }
    },

    async updateUserStatus(user) {
      try {
        const response = await fetch(`/api/users/${user.id}/status?status=${user.status}`, {
          method: 'PUT'
        })
        const result = await response.json()
        if (result.code === 200) {
          ElMessage.success('用户状态更新成功')
        } else {
          ElMessage.error(result.message || '更新失败')
          // 恢复原状态
          user.status = user.status === 1 ? 0 : 1
        }
      } catch (error) {
        console.error('更新用户状态失败:', error)
        ElMessage.error('更新失败')
        user.status = user.status === 1 ? 0 : 1
      }
    },

    async deleteUser(user) {
      try {
        await ElMessageBox.confirm(`确定要删除用户 "${user.username}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await fetch(`/api/users/${user.id}`, {
          method: 'DELETE'
        })
        const result = await response.json()
        if (result.code === 200) {
          ElMessage.success('用户删除成功')
          this.loadUsers()
        } else {
          ElMessage.error(result.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除用户失败:', error)
          ElMessage.error('删除失败')
        }
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.user-manage-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.search-bar {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}
</style>
