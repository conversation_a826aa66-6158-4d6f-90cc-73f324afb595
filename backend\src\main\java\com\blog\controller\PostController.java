package com.blog.controller;

import com.blog.common.Result;
import com.blog.entity.Post;
import com.blog.entity.User;
import com.blog.service.PostService;
import com.blog.service.UserService;
import com.blog.util.AuthUtil;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 博客文章控制器
 */
@RestController
@RequestMapping("/api/posts")
public class PostController {

    @Autowired
    private PostService postService;

    @Autowired
    private UserService userService;

    /**
     * 根据ID查询文章
     */
    @GetMapping("/{id}")
    public Result<Post> getPostById(@PathVariable Long id) {
        try {
            Post post = postService.findDetailById(id);
            if (post != null) {
                // 增加浏览次数
                postService.incrementViewCount(id);
                return Result.success(post);
            } else {
                return Result.error("文章不存在");
            }
        } catch (Exception e) {
            return Result.error("查询文章失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有文章
     */
    @GetMapping
    public Result<List<Post>> getAllPosts() {
        try {
            List<Post> posts = postService.findAll();
            return Result.success(posts);
        } catch (Exception e) {
            return Result.error("查询文章列表失败：" + e.getMessage());
        }
    }

    /**
     * 查询已发布的文章
     */
    @GetMapping("/published")
    public Result<List<Post>> getPublishedPosts() {
        try {
            List<Post> posts = postService.findPublished();
            return Result.success(posts);
        } catch (Exception e) {
            return Result.error("查询已发布文章失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询文章
     */
    @GetMapping("/page")
    public Result<PageInfo<Post>> getPostsByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            PageInfo<Post> pageInfo = postService.findByPage(pageNum, pageSize);
            return Result.success(pageInfo);
        } catch (Exception e) {
            return Result.error("分页查询文章失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询已发布文章
     */
    @GetMapping("/published/page")
    public Result<PageInfo<Post>> getPublishedPostsByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            PageInfo<Post> pageInfo = postService.findPublishedByPage(pageNum, pageSize);
            return Result.success(pageInfo);
        } catch (Exception e) {
            return Result.error("分页查询已发布文章失败：" + e.getMessage());
        }
    }

    /**
     * 根据作者ID查询文章
     */
    @GetMapping("/author/{authorId}")
    public Result<List<Post>> getPostsByAuthor(@PathVariable Long authorId) {
        try {
            List<Post> posts = postService.findByAuthorId(authorId);
            return Result.success(posts);
        } catch (Exception e) {
            return Result.error("查询作者文章失败：" + e.getMessage());
        }
    }

    /**
     * 根据分类ID查询文章
     */
    @GetMapping("/category/{categoryId}")
    public Result<List<Post>> getPostsByCategory(@PathVariable Long categoryId) {
        try {
            List<Post> posts = postService.findByCategoryId(categoryId);
            return Result.success(posts);
        } catch (Exception e) {
            return Result.error("查询分类文章失败：" + e.getMessage());
        }
    }

    /**
     * 根据关键词搜索文章
     */
    @GetMapping("/search")
    public Result<List<Post>> searchPosts(@RequestParam String keyword) {
        try {
            List<Post> posts = postService.searchByKeyword(keyword);
            return Result.success(posts);
        } catch (Exception e) {
            return Result.error("搜索文章失败：" + e.getMessage());
        }
    }

    /**
     * 创建文章
     */
    @PostMapping
    public Result<Post> createPost(@Valid @RequestBody Post post) {
        try {
            Post createdPost = postService.createPost(post);
            return Result.success("文章创建成功", createdPost);
        } catch (Exception e) {
            return Result.error("创建文章失败：" + e.getMessage());
        }
    }

    /**
     * 更新文章信息
     */
    @PutMapping("/{id}")
    public Result<Post> updatePost(@PathVariable Long id, @Valid @RequestBody Post post) {
        try {
            post.setId(id);
            Post updatedPost = postService.updatePost(post);
            return Result.success("文章更新成功", updatedPost);
        } catch (Exception e) {
            return Result.error("更新文章失败：" + e.getMessage());
        }
    }

    /**
     * 删除文章
     */
    @DeleteMapping("/{id}")
    public Result<String> deletePost(@PathVariable Long id) {
        try {
            boolean success = postService.deleteById(id);
            if (success) {
                return Result.success("文章删除成功");
            } else {
                return Result.error("删除文章失败");
            }
        } catch (Exception e) {
            return Result.error("删除文章失败：" + e.getMessage());
        }
    }

    /**
     * 发布文章
     */
    @PutMapping("/{id}/publish")
    public Result<String> publishPost(@PathVariable Long id) {
        try {
            boolean success = postService.publishPost(id);
            if (success) {
                return Result.success("文章发布成功");
            } else {
                return Result.error("发布文章失败");
            }
        } catch (Exception e) {
            return Result.error("发布文章失败：" + e.getMessage());
        }
    }

    /**
     * 撤回文章
     */
    @PutMapping("/{id}/unpublish")
    public Result<String> unpublishPost(@PathVariable Long id) {
        try {
            boolean success = postService.unpublishPost(id);
            if (success) {
                return Result.success("文章撤回成功");
            } else {
                return Result.error("撤回文章失败");
            }
        } catch (Exception e) {
            return Result.error("撤回文章失败：" + e.getMessage());
        }
    }

    /**
     * 点赞文章
     */
    @PutMapping("/{id}/like")
    public Result<String> likePost(@PathVariable Long id) {
        try {
            boolean success = postService.incrementLikeCount(id);
            if (success) {
                return Result.success("点赞成功");
            } else {
                return Result.error("点赞失败");
            }
        } catch (Exception e) {
            return Result.error("点赞失败：" + e.getMessage());
        }
    }

    /**
     * 获取文章统计信息
     */
    @GetMapping("/stats")
    public Result<Object> getPostStats() {
        try {
            Long postsCount = postService.getPublishedCount(); // 只统计已发布的文章
            Long viewsCount = postService.getTotalViews();
            Long likesCount = postService.getTotalLikes();
            Long commentsCount = postService.getTotalComments();

            return Result.success(new Object() {
                @SuppressWarnings("unused")
                public final Long totalPosts = postsCount;
                @SuppressWarnings("unused")
                public final Long totalViews = viewsCount;
                @SuppressWarnings("unused")
                public final Long totalLikes = likesCount;
                @SuppressWarnings("unused")
                public final Long totalComments = commentsCount;
            });
        } catch (Exception e) {
            return Result.error("获取统计信息失败：" + e.getMessage());
        }
    }

    /**
     * 提交审核
     */
    @PutMapping("/{id}/submit-review")
    public Result<Post> submitForReview(@PathVariable Long id) {
        try {
            Post post = postService.submitForReview(id);
            if (post != null) {
                return Result.success("文章已提交审核", post);
            } else {
                return Result.error("提交审核失败");
            }
        } catch (Exception e) {
            return Result.error("提交审核失败：" + e.getMessage());
        }
    }

    /**
     * 审核通过 - 仅管理员可操作
     */
    @PutMapping("/{id}/approve")
    public Result<Post> approvePost(@PathVariable Long id,
            @RequestParam Long reviewerId,
            @RequestParam(required = false) String reviewComment) {
        try {
            // 权限检查：只有管理员可以审核
            User reviewer = userService.findById(reviewerId);
            if (!AuthUtil.hasReviewPermission(reviewer)) {
                return Result.error("权限不足，只有管理员可以审核文章");
            }

            Post post = postService.approvePost(id, reviewerId, reviewComment);
            if (post != null) {
                return Result.success("文章审核通过", post);
            } else {
                return Result.error("审核失败");
            }
        } catch (Exception e) {
            return Result.error("审核失败：" + e.getMessage());
        }
    }

    /**
     * 审核拒绝 - 仅管理员可操作
     */
    @PutMapping("/{id}/reject")
    public Result<Post> rejectPost(@PathVariable Long id,
            @RequestParam Long reviewerId,
            @RequestParam String reviewComment) {
        try {
            // 权限检查：只有管理员可以审核
            User reviewer = userService.findById(reviewerId);
            if (!AuthUtil.hasReviewPermission(reviewer)) {
                return Result.error("权限不足，只有管理员可以审核文章");
            }

            Post post = postService.rejectPost(id, reviewerId, reviewComment);
            if (post != null) {
                return Result.success("文章审核拒绝", post);
            } else {
                return Result.error("审核失败");
            }
        } catch (Exception e) {
            return Result.error("审核失败：" + e.getMessage());
        }
    }

    /**
     * 获取待审核文章列表 - 仅管理员可查看
     */
    @GetMapping("/pending-review/page")
    public Result<PageInfo<Post>> getPendingReviewPosts(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam Long userId) {
        try {
            // 权限检查：只有管理员可以查看待审核文章列表
            User user = userService.findById(userId);
            if (!AuthUtil.hasReviewPermission(user)) {
                return Result.error("权限不足，只有管理员可以查看待审核文章");
            }

            PageInfo<Post> pageInfo = postService.findPendingReviewByPage(pageNum, pageSize);
            return Result.success(pageInfo);
        } catch (Exception e) {
            return Result.error("查询待审核文章失败：" + e.getMessage());
        }
    }

    /**
     * 获取已审核文章列表 - 仅管理员可查看
     */
    @GetMapping("/reviewed/page")
    public Result<PageInfo<Post>> getReviewedPosts(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            @RequestParam Long userId) {
        try {
            // 权限检查：只有管理员可以查看已审核文章列表
            User user = userService.findById(userId);
            if (!AuthUtil.hasReviewPermission(user)) {
                return Result.error("权限不足，只有管理员可以查看已审核文章");
            }

            PageInfo<Post> pageInfo = postService.findReviewedByPage(pageNum, pageSize);
            return Result.success(pageInfo);
        } catch (Exception e) {
            return Result.error("查询已审核文章失败：" + e.getMessage());
        }
    }
}
