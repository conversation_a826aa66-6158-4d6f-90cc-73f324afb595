import { createRouter, createWebHistory } from 'vue-router'
import { userStore } from '../store/user'

// 路由懒加载
const Home = () => import('../views/Home.vue')
const Posts = () => import('../views/Posts.vue')
const PostDetail = () => import('../views/PostDetail.vue')
const PostEditor = () => import('../views/PostEditor.vue')
const PostManage = () => import('../views/PostManage.vue')
const PostReview = () => import('../views/PostReview.vue')
const Categories = () => import('../views/Categories.vue')
const About = () => import('../views/About.vue')
const Profile = () => import('../views/Profile.vue')
const UserManage = () => import('../views/admin/UserManage.vue')
const CategoryManage = () => import('../views/admin/CategoryManage.vue')
const Login = () => import('../views/Login.vue')
const Register = () => import('../views/Register.vue')

const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: { hideNavigation: true }
  },
  {
    path: '/posts',
    name: 'Posts',
    component: Posts,
    meta: { hideNavigation: true }
  },
  {
    path: '/posts/:id',
    name: 'PostDetail',
    component: PostDetail,
    meta: { hideNavigation: true }
  },
  {
    path: '/editor',
    name: 'PostEditor',
    component: PostEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/editor/:id',
    name: 'PostEditorEdit',
    component: PostEditor,
    meta: { requiresAuth: true }
  },
  {
    path: '/manage',
    name: 'PostManage',
    component: PostManage,
    meta: { requiresAuth: true }
  },
  {
    path: '/review',
    name: 'PostReview',
    component: PostReview,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/categories',
    name: 'Categories',
    component: Categories
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: { requiresAuth: true }
  },
  {
    path: '/admin/users',
    name: 'UserManage',
    component: UserManage,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/admin/categories',
    name: 'CategoryManage',
    component: CategoryManage,
    meta: { requiresAuth: true, requiresAdmin: true }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { hideNavigation: true }
  },
  {
    path: '/register',
    name: 'Register',
    component: Register,
    meta: { hideNavigation: true }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 初始化用户状态
  userStore.init()

  // 需要登录的页面
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  // 需要管理员权限的页面
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  if (requiresAuth && !userStore.isLoggedIn) {
    next({
      path: '/login',
      query: { redirect: to.fullPath }
    })
  } else if (requiresAdmin && !userStore.hasReviewPermission()) {
    // 需要管理员权限但用户不是管理员
    next({
      path: '/posts',
      query: { error: 'permission_denied' }
    })
  } else {
    next()
  }
})

export default router
