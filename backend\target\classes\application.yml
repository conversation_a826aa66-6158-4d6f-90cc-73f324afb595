server:
  port: 8080
  servlet:
    context-path: /blog

spring:
  application:
    name: spring-boot-blog

  # 数据源配置
  datasource:
    url: *********************************************************************************************************************************************
    username: root
    password: xiaoming@11MQ
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 2MB
      max-request-size: 10MB

# MyBatis配置
mybatis:
  # 指定mapper xml文件位置
  mapper-locations: classpath:mapper/*.xml
  # 指定实体类包路径
  type-aliases-package: com.blog.entity
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启二级缓存
    cache-enabled: true
    # 延迟加载
    lazy-loading-enabled: true
    # 积极延迟加载
    aggressive-lazy-loading: false

# PageHelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

# 文件上传配置
file:
  upload:
    path: ./uploads
    url-prefix: http://localhost:8080/blog/uploads

# 日志配置
logging:
  level:
    '[com.blog.mapper]': debug
    '[org.springframework.web]': info
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
