package com.blog.service.impl;

import com.blog.entity.User;
import com.blog.mapper.UserMapper;
import com.blog.service.UserService;
import com.blog.util.PasswordMigrationUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    private final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Override
    public User findById(Long id) {
        return userMapper.selectById(id);
    }

    @Override
    public User findByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        return userMapper.selectByEmail(email);
    }

    @Override
    public List<User> findAll() {
        return userMapper.selectAll();
    }

    @Override
    public PageInfo<User> findByPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<User> users = userMapper.selectAll();
        return new PageInfo<>(users);
    }

    @Override
    public User createUser(User user) {
        // 设置创建时间和更新时间
        user.setCreateTime(LocalDateTime.now());
        user.setUpdateTime(LocalDateTime.now());

        // 如果没有设置状态，默认为启用
        if (user.getStatus() == null) {
            user.setStatus(1);
        }

        // 如果没有设置昵称，使用用户名
        if (user.getNickname() == null || user.getNickname().trim().isEmpty()) {
            user.setNickname(user.getUsername());
        }

        // 密码加密
        if (user.getPassword() != null) {
            user.setPassword(encryptPassword(user.getPassword()));
        }

        userMapper.insert(user);
        return user;
    }

    @Override
    public User updateUser(User user) {
        user.setUpdateTime(LocalDateTime.now());

        // 如果密码不为空，进行加密
        if (user.getPassword() != null && !user.getPassword().trim().isEmpty()) {
            user.setPassword(encryptPassword(user.getPassword()));
        } else {
            // 如果密码为空，不更新密码字段
            user.setPassword(null);
        }

        userMapper.update(user);
        return findById(user.getId());
    }

    @Override
    public boolean deleteById(Long id) {
        return userMapper.deleteById(id) > 0;
    }

    @Override
    public boolean updateStatus(Long id, Integer status) {
        return userMapper.updateStatus(id, status) > 0;
    }

    @Override
    public User login(String username, String password) {
        User user = userMapper.selectByUsername(username);
        if (user != null && user.getStatus() == 1) {
            // 支持密码迁移的验证
            String newHash = PasswordMigrationUtil.verifyAndMigrate(password, user.getPassword());
            if (newHash != null) {
                // 密码验证成功，且需要更新为新的BCrypt哈希
                user.setPassword(newHash);
                userMapper.update(user);
                return user;
            } else if (!PasswordMigrationUtil.isOldMD5Password(user.getPassword())) {
                // 已经是BCrypt密码，直接验证
                if (verifyPassword(password, user.getPassword())) {
                    return user;
                }
            }
        }
        return null;
    }

    @Override
    public User register(String username, String password, String email) {
        // 检查用户名和邮箱是否已存在
        if (existsByUsername(username)) {
            throw new RuntimeException("用户名已存在");
        }
        if (existsByEmail(email)) {
            throw new RuntimeException("邮箱已存在");
        }

        User user = new User(username, password, email);
        return createUser(user);
    }

    @Override
    public boolean existsByUsername(String username) {
        return userMapper.selectByUsername(username) != null;
    }

    @Override
    public boolean existsByEmail(String email) {
        return userMapper.selectByEmail(email) != null;
    }

    @Override
    public boolean changePassword(Long userId, String currentPassword, String newPassword) {
        User user = findById(userId);
        if (user == null) {
            return false;
        }

        // 验证当前密码
        if (!verifyPassword(currentPassword, user.getPassword())) {
            return false;
        }

        // 更新为新密码
        user.setPassword(encryptPassword(newPassword));
        user.setUpdateTime(LocalDateTime.now());
        userMapper.update(user);
        return true;
    }

    /**
     * 密码加密 - 使用BCrypt算法
     */
    private String encryptPassword(String password) {
        return passwordEncoder.encode(password);
    }

    /**
     * 验证密码
     */
    private boolean verifyPassword(String rawPassword, String encodedPassword) {
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }
}
