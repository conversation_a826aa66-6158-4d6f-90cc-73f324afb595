package com.blog.controller;

import com.blog.common.Result;
import com.blog.entity.User;
import com.blog.service.LoginAttemptService;
import com.blog.service.UserService;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/users")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private LoginAttemptService loginAttemptService;

    /**
     * 根据ID查询用户
     */
    @GetMapping("/{id}")
    public Result<User> getUserById(@PathVariable Long id) {
        try {
            User user = userService.findById(id);
            if (user != null) {
                return Result.success(user);
            } else {
                return Result.error("用户不存在");
            }
        } catch (Exception e) {
            return Result.error("查询用户失败：" + e.getMessage());
        }
    }

    /**
     * 查询所有用户
     */
    @GetMapping
    public Result<List<User>> getAllUsers() {
        try {
            List<User> users = userService.findAll();
            return Result.success(users);
        } catch (Exception e) {
            return Result.error("查询用户列表失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询用户
     */
    @GetMapping("/page")
    public Result<PageInfo<User>> getUsersByPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        try {
            PageInfo<User> pageInfo = userService.findByPage(pageNum, pageSize);
            return Result.success(pageInfo);
        } catch (Exception e) {
            return Result.error("分页查询用户失败：" + e.getMessage());
        }
    }

    /**
     * 创建用户
     */
    @PostMapping
    public Result<User> createUser(@Valid @RequestBody User user) {
        try {
            User createdUser = userService.createUser(user);
            return Result.success("用户创建成功", createdUser);
        } catch (Exception e) {
            return Result.error("创建用户失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/{id}")
    public Result<User> updateUser(@PathVariable Long id, @Valid @RequestBody User user) {
        try {
            user.setId(id);
            User updatedUser = userService.updateUser(user);
            return Result.success("用户更新成功", updatedUser);
        } catch (Exception e) {
            return Result.error("更新用户失败：" + e.getMessage());
        }
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    public Result<String> deleteUser(@PathVariable Long id) {
        try {
            boolean success = userService.deleteById(id);
            if (success) {
                return Result.success("用户删除成功");
            } else {
                return Result.error("删除用户失败");
            }
        } catch (Exception e) {
            return Result.error("删除用户失败：" + e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/{id}/status")
    public Result<String> updateUserStatus(@PathVariable Long id, @RequestParam Integer status) {
        try {
            boolean success = userService.updateStatus(id, status);
            if (success) {
                return Result.success("用户状态更新成功");
            } else {
                return Result.error("更新用户状态失败");
            }
        } catch (Exception e) {
            return Result.error("更新用户状态失败：" + e.getMessage());
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public Result<User> login(@RequestParam String username,
            @RequestParam String password,
            HttpServletRequest request) {
        try {
            // 获取客户端IP地址
            String clientIP = getClientIP(request);
            String loginKey = username + ":" + clientIP;

            // 检查是否被锁定
            if (loginAttemptService.isBlocked(loginKey)) {
                long remainingTime = loginAttemptService.getRemainingLockTime(loginKey);
                return Result.error("登录失败次数过多，请在 " + remainingTime + " 分钟后重试");
            }

            // 基本参数验证
            if (username == null || username.trim().isEmpty()) {
                return Result.error("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }

            User user = userService.login(username.trim(), password);
            if (user != null) {
                // 登录成功，清除失败记录
                loginAttemptService.loginSucceeded(loginKey);

                // 清除密码字段，避免返回给前端
                user.setPassword(null);
                return Result.success("登录成功", user);
            } else {
                // 登录失败，记录失败次数
                loginAttemptService.loginFailed(loginKey);
                int failedAttempts = loginAttemptService.getFailedAttempts(loginKey);
                int remainingAttempts = 5 - failedAttempts;

                if (remainingAttempts > 0) {
                    return Result.error("用户名或密码错误，还有 " + remainingAttempts + " 次尝试机会");
                } else {
                    return Result.error("登录失败次数过多，账户已被锁定15分钟");
                }
            }
        } catch (Exception e) {
            return Result.error("登录失败：" + e.getMessage());
        }
    }

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public Result<User> register(@RequestParam String username,
            @RequestParam String password,
            @RequestParam String email) {
        try {
            // 基本参数验证
            if (username == null || username.trim().isEmpty()) {
                return Result.error("用户名不能为空");
            }
            if (password == null || password.trim().isEmpty()) {
                return Result.error("密码不能为空");
            }
            if (email == null || email.trim().isEmpty()) {
                return Result.error("邮箱不能为空");
            }

            // 密码强度验证
            if (password.length() < 6) {
                return Result.error("密码长度不能少于6位");
            }
            if (password.length() > 20) {
                return Result.error("密码长度不能超过20位");
            }

            // 邮箱格式验证
            if (!isValidEmail(email.trim())) {
                return Result.error("邮箱格式不正确");
            }

            User user = userService.register(username.trim(), password, email.trim());
            // 清除密码字段，避免返回给前端
            user.setPassword(null);
            return Result.success("注册成功", user);
        } catch (Exception e) {
            return Result.error("注册失败：" + e.getMessage());
        }
    }

    /**
     * 获取客户端真实IP地址
     */
    private String getClientIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIP = request.getHeader("X-Real-IP");
        if (xRealIP != null && !xRealIP.isEmpty() && !"unknown".equalsIgnoreCase(xRealIP)) {
            return xRealIP;
        }

        return request.getRemoteAddr();
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmail(String email) {
        String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\.[a-zA-Z0-9_+&*-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}$";
        return email.matches(emailRegex);
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    public Result<Boolean> checkUsername(@RequestParam String username) {
        try {
            boolean exists = userService.existsByUsername(username);
            return Result.success(exists);
        } catch (Exception e) {
            return Result.error("检查用户名失败：" + e.getMessage());
        }
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    public Result<Boolean> checkEmail(@RequestParam String email) {
        try {
            boolean exists = userService.existsByEmail(email);
            return Result.success(exists);
        } catch (Exception e) {
            return Result.error("检查邮箱失败：" + e.getMessage());
        }
    }

    /**
     * 修改密码
     */
    @PostMapping("/change-password")
    public Result<String> changePassword(@RequestBody ChangePasswordRequest request) {
        try {
            // 基本参数验证
            if (request.getCurrentPassword() == null || request.getCurrentPassword().trim().isEmpty()) {
                return Result.error("当前密码不能为空");
            }
            if (request.getNewPassword() == null || request.getNewPassword().trim().isEmpty()) {
                return Result.error("新密码不能为空");
            }
            if (request.getConfirmPassword() == null || request.getConfirmPassword().trim().isEmpty()) {
                return Result.error("确认密码不能为空");
            }

            // 新密码强度验证
            if (request.getNewPassword().length() < 6) {
                return Result.error("新密码长度不能少于6位");
            }
            if (request.getNewPassword().length() > 20) {
                return Result.error("新密码长度不能超过20位");
            }

            // 确认密码验证
            if (!request.getNewPassword().equals(request.getConfirmPassword())) {
                return Result.error("两次输入的密码不一致");
            }

            boolean success = userService.changePassword(request.getUserId(),
                    request.getCurrentPassword(), request.getNewPassword());
            if (success) {
                return Result.success("密码修改成功");
            } else {
                return Result.error("当前密码不正确");
            }
        } catch (Exception e) {
            return Result.error("修改密码失败：" + e.getMessage());
        }
    }

    /**
     * 修改密码请求类
     */
    public static class ChangePasswordRequest {
        private Long userId;
        private String currentPassword;
        private String newPassword;
        private String confirmPassword;

        // Getters and Setters
        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }

        public String getCurrentPassword() {
            return currentPassword;
        }

        public void setCurrentPassword(String currentPassword) {
            this.currentPassword = currentPassword;
        }

        public String getNewPassword() {
            return newPassword;
        }

        public void setNewPassword(String newPassword) {
            this.newPassword = newPassword;
        }

        public String getConfirmPassword() {
            return confirmPassword;
        }

        public void setConfirmPassword(String confirmPassword) {
            this.confirmPassword = confirmPassword;
        }
    }
}
