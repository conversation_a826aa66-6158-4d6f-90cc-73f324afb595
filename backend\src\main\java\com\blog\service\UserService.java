package com.blog.service;

import com.blog.entity.User;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {

    /**
     * 根据ID查询用户
     */
    User findById(Long id);

    /**
     * 根据用户名查询用户
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     */
    User findByEmail(String email);

    /**
     * 查询所有用户
     */
    List<User> findAll();

    /**
     * 分页查询用户
     */
    PageInfo<User> findByPage(Integer pageNum, Integer pageSize);

    /**
     * 创建用户
     */
    User createUser(User user);

    /**
     * 更新用户信息
     */
    User updateUser(User user);

    /**
     * 根据ID删除用户
     */
    boolean deleteById(Long id);

    /**
     * 更新用户状态
     */
    boolean updateStatus(Long id, Integer status);

    /**
     * 用户登录验证
     */
    User login(String username, String password);

    /**
     * 用户注册
     */
    User register(String username, String password, String email);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 修改密码
     */
    boolean changePassword(Long userId, String currentPassword, String newPassword);
}
