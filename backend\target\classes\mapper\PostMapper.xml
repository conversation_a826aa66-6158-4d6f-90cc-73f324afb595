<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.blog.mapper.PostMapper">

    <!-- 结果映射 -->
    <resultMap id="PostResultMap" type="com.blog.entity.Post">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="summary" column="summary"/>
        <result property="content" column="content"/>
        <result property="coverImage" column="cover_image"/>
        <result property="authorId" column="author_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="tags" column="tags"/>
        <result property="viewCount" column="view_count"/>
        <result property="likeCount" column="like_count"/>
        <result property="commentCount" column="comment_count"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="publishTime" column="publish_time"/>
        <!-- 关联对象名称 -->
        <result property="authorName" column="author_name"/>
        <result property="categoryName" column="category_name"/>
        <result property="reviewerName" column="reviewer_name"/>
    </resultMap>

    <!-- 详情结果映射（包含作者和分类信息） -->
    <resultMap id="PostDetailResultMap" type="com.blog.entity.Post" extends="PostResultMap">
        <association property="author" javaType="com.blog.entity.User">
            <id property="id" column="author_id"/>
            <result property="username" column="author_username"/>
            <result property="nickname" column="author_nickname"/>
            <result property="avatar" column="author_avatar"/>
        </association>
        <association property="category" javaType="com.blog.entity.Category">
            <id property="id" column="category_id"/>
            <result property="name" column="category_name"/>
            <result property="description" column="category_description"/>
        </association>
    </resultMap>

    <!-- 基础查询字段 -->
    <sql id="Base_Column_List">
        id, title, summary, content, cover_image, author_id, category_id, tags, 
        view_count, like_count, comment_count, status, create_time, update_time, publish_time
    </sql>

    <!-- 根据ID查询文章 -->
    <select id="selectById" resultMap="PostResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM posts
        WHERE id = #{id}
    </select>

    <!-- 根据ID查询文章详情（包含作者和分类信息） -->
    <select id="selectDetailById" resultMap="PostDetailResultMap">
        SELECT p.*, 
               u.username as author_username, u.nickname as author_nickname, u.avatar as author_avatar,
               c.name as category_name, c.description as category_description
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.id = #{id}
    </select>

    <!-- 查询所有文章 -->
    <select id="selectAll" resultMap="PostResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM posts
        ORDER BY create_time DESC
    </select>

    <!-- 查询已发布的文章 -->
    <select id="selectPublished" resultMap="PostResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM posts
        WHERE status = 2
        ORDER BY publish_time DESC
    </select>

    <!-- 根据作者ID查询文章 -->
    <select id="selectByAuthorId" resultMap="PostResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM posts
        WHERE author_id = #{authorId}
        ORDER BY create_time DESC
    </select>

    <!-- 根据分类ID查询文章 -->
    <select id="selectByCategoryId" resultMap="PostResultMap">
        SELECT p.*, u.username as author_name, c.name as category_name
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.category_id = #{categoryId} AND p.status = 2
        ORDER BY p.publish_time DESC
    </select>

    <!-- 根据关键词搜索文章（标题和内容） -->
    <select id="selectByTitle" resultMap="PostResultMap">
        SELECT p.*, u.username as author_name, c.name as category_name
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE (p.title LIKE CONCAT('%', #{title}, '%') OR p.content LIKE CONCAT('%', #{title}, '%')) AND p.status = 2
        ORDER BY p.publish_time DESC
    </select>

    <!-- 分页查询文章 -->
    <select id="selectByPage" resultMap="PostResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM posts
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 分页查询已发布文章 -->
    <select id="selectPublishedByPage" resultMap="PostResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM posts
        WHERE status = 2
        ORDER BY publish_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计文章总数 -->
    <select id="countAll" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM posts
    </select>

    <!-- 统计已发布文章总数 -->
    <select id="countPublished" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM posts
        WHERE status = 2
    </select>

    <!-- 插入文章 -->
    <insert id="insert" parameterType="com.blog.entity.Post" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO posts (title, summary, content, cover_image, author_id, category_id, tags,
                          view_count, like_count, comment_count, status, create_time, update_time, publish_time,
                          reviewer_id, review_time, review_comment)
        VALUES (#{title}, #{summary}, #{content}, #{coverImage}, #{authorId}, #{categoryId}, #{tags},
                #{viewCount}, #{likeCount}, #{commentCount}, #{status}, #{createTime}, #{updateTime}, #{publishTime},
                #{reviewerId}, #{reviewTime}, #{reviewComment})
    </insert>

    <!-- 更新文章信息 -->
    <update id="update" parameterType="com.blog.entity.Post">
        UPDATE posts
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="summary != null">summary = #{summary},</if>
            <if test="content != null">content = #{content},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="status != null">status = #{status},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除文章 -->
    <delete id="deleteById">
        DELETE FROM posts WHERE id = #{id}
    </delete>

    <!-- 更新文章状态 -->
    <update id="updateStatus">
        UPDATE posts SET status = #{status}, update_time = NOW()
        <if test="status == 1">, publish_time = NOW()</if>
        WHERE id = #{id}
    </update>

    <!-- 增加浏览次数 -->
    <update id="incrementViewCount">
        UPDATE posts SET view_count = view_count + 1
        WHERE id = #{id}
    </update>

    <!-- 增加点赞次数 -->
    <update id="incrementLikeCount">
        UPDATE posts SET like_count = like_count + 1
        WHERE id = #{id}
    </update>

    <!-- 根据状态查询文章 -->
    <select id="selectByStatus" parameterType="Integer" resultMap="PostResultMap">
        SELECT p.*, u.username as author_name, c.name as category_name
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        WHERE p.status = #{status}
        ORDER BY p.create_time DESC
    </select>

    <!-- 查询已审核的文章 -->
    <select id="selectReviewed" resultMap="PostResultMap">
        SELECT p.*, u.username as author_name, c.name as category_name,
               r.username as reviewer_name
        FROM posts p
        LEFT JOIN users u ON p.author_id = u.id
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN users r ON p.reviewer_id = r.id
        WHERE p.status IN (2, 3)
        ORDER BY p.review_time DESC
    </select>

</mapper>
