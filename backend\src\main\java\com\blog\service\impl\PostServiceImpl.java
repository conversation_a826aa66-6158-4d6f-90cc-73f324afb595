package com.blog.service.impl;

import com.blog.entity.Post;
import com.blog.mapper.PostMapper;
import com.blog.service.PostService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 博客文章服务实现类
 */
@Service
public class PostServiceImpl implements PostService {

    @Autowired
    private PostMapper postMapper;

    @Override
    public Post findById(Long id) {
        return postMapper.selectById(id);
    }

    @Override
    public Post findDetailById(Long id) {
        return postMapper.selectDetailById(id);
    }

    @Override
    public List<Post> findAll() {
        return postMapper.selectAll();
    }

    @Override
    public List<Post> findPublished() {
        return postMapper.selectPublished();
    }

    @Override
    public List<Post> findByAuthorId(Long authorId) {
        return postMapper.selectByAuthorId(authorId);
    }

    @Override
    public List<Post> findByCategoryId(Long categoryId) {
        return postMapper.selectByCategoryId(categoryId);
    }

    @Override
    public List<Post> searchByKeyword(String keyword) {
        return postMapper.selectByTitle(keyword);
    }

    @Override
    public PageInfo<Post> findByPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Post> posts = postMapper.selectAll();
        return new PageInfo<>(posts);
    }

    @Override
    public PageInfo<Post> findPublishedByPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Post> posts = postMapper.selectPublished();
        return new PageInfo<>(posts);
    }

    @Override
    public Post createPost(Post post) {
        // 设置创建时间和更新时间
        post.setCreateTime(LocalDateTime.now());
        post.setUpdateTime(LocalDateTime.now());

        // 如果没有设置状态，默认为草稿
        if (post.getStatus() == null) {
            post.setStatus(0);
        }

        // 初始化计数器
        if (post.getViewCount() == null) {
            post.setViewCount(0);
        }
        if (post.getLikeCount() == null) {
            post.setLikeCount(0);
        }
        if (post.getCommentCount() == null) {
            post.setCommentCount(0);
        }

        // 如果是发布状态，设置为待审核
        if (post.getStatus() == 1) {
            post.setStatus(1); // 1-待审核
        }

        // 如果没有摘要，从内容中提取前200个字符
        if (post.getSummary() == null || post.getSummary().trim().isEmpty()) {
            if (post.getContent() != null && post.getContent().length() > 200) {
                post.setSummary(post.getContent().substring(0, 200) + "...");
            } else {
                post.setSummary(post.getContent());
            }
        }

        postMapper.insert(post);
        return post;
    }

    @Override
    public Post updatePost(Post post) {
        post.setUpdateTime(LocalDateTime.now());

        // 如果状态改为发布且之前没有发布时间，设置发布时间
        if (post.getStatus() != null && post.getStatus() == 1) {
            Post existingPost = postMapper.selectById(post.getId());
            if (existingPost != null && existingPost.getPublishTime() == null) {
                post.setPublishTime(LocalDateTime.now());
            }
        }

        postMapper.update(post);
        return findById(post.getId());
    }

    @Override
    public boolean deleteById(Long id) {
        return postMapper.deleteById(id) > 0;
    }

    @Override
    public boolean publishPost(Long id) {
        return postMapper.updateStatus(id, 1) > 0;
    }

    @Override
    public boolean unpublishPost(Long id) {
        return postMapper.updateStatus(id, 0) > 0;
    }

    @Override
    public boolean incrementViewCount(Long id) {
        return postMapper.incrementViewCount(id) > 0;
    }

    @Override
    public boolean incrementLikeCount(Long id) {
        return postMapper.incrementLikeCount(id) > 0;
    }

    @Override
    public Long getTotalCount() {
        return postMapper.countAll().longValue();
    }

    @Override
    public Long getPublishedCount() {
        return postMapper.countPublished().longValue();
    }

    @Override
    public Long getTotalViews() {
        return postMapper.sumTotalViews();
    }

    @Override
    public Long getTotalLikes() {
        return postMapper.sumTotalLikes();
    }

    @Override
    public Long getTotalComments() {
        return postMapper.sumTotalComments();
    }

    @Override
    public Post submitForReview(Long id) {
        Post post = postMapper.selectById(id);
        if (post != null && post.getStatus() == 0) { // 只有草稿可以提交审核
            post.setStatus(1); // 1-待审核
            post.setUpdateTime(LocalDateTime.now());
            postMapper.update(post);
        }
        return post;
    }

    @Override
    public Post approvePost(Long id, Long reviewerId, String reviewComment) {
        Post post = postMapper.selectById(id);
        if (post != null && post.getStatus() == 1) { // 只有待审核的文章可以审核通过
            post.setStatus(2); // 2-已发布
            post.setReviewerId(reviewerId);
            post.setReviewTime(LocalDateTime.now());
            post.setReviewComment(reviewComment);
            post.setPublishTime(LocalDateTime.now());
            post.setUpdateTime(LocalDateTime.now());
            postMapper.update(post);
        }
        return post;
    }

    @Override
    public Post rejectPost(Long id, Long reviewerId, String reviewComment) {
        Post post = postMapper.selectById(id);
        if (post != null && post.getStatus() == 1) { // 只有待审核的文章可以审核拒绝
            post.setStatus(3); // 3-审核拒绝
            post.setReviewerId(reviewerId);
            post.setReviewTime(LocalDateTime.now());
            post.setReviewComment(reviewComment);
            post.setUpdateTime(LocalDateTime.now());
            postMapper.update(post);
        }
        return post;
    }

    @Override
    public PageInfo<Post> findPendingReviewByPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Post> posts = postMapper.selectByStatus(1); // 1-待审核
        return new PageInfo<>(posts);
    }

    @Override
    public PageInfo<Post> findReviewedByPage(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<Post> posts = postMapper.selectReviewed(); // 已审核的文章（通过或拒绝）
        return new PageInfo<>(posts);
    }
}
