<template>
  <div class="category-manage-page">
    <div class="page-header">
      <h2>分类管理</h2>
      <el-button type="primary" @click="showCreateDialog">
        <el-icon><Plus /></el-icon>
        添加分类
      </el-button>
    </div>

    <!-- 分类表格 -->
    <el-table :data="categories" v-loading="loading" stripe>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="分类名称" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="postCount" label="文章数量" width="100" />
      <el-table-column prop="createTime" label="创建时间" width="180">
        <template #default="{ row }">
          {{ formatDate(row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editCategory(row)">编辑</el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="deleteCategory(row)"
            :disabled="row.postCount > 0"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 创建/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑分类' : '添加分类'"
      width="500px"
    >
      <el-form
        ref="categoryForm"
        :model="categoryForm"
        :rules="categoryRules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="categoryForm.name" />
        </el-form-item>

        <el-form-item label="描述" prop="description">
          <el-input
            v-model="categoryForm.description"
            type="textarea"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveCategory" :loading="saving">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  name: 'CategoryManage',
  data() {
    return {
      loading: false,
      saving: false,
      categories: [],
      dialogVisible: false,
      isEdit: false,
      categoryForm: {
        name: '',
        description: ''
      },
      categoryRules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' },
          { min: 2, max: 20, message: '分类名称长度在 2 到 20 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  mounted() {
    this.loadCategories()
  },
  methods: {
    async loadCategories() {
      try {
        this.loading = true
        const response = await fetch('/api/categories')
        const result = await response.json()
        
        if (result.code === 200) {
          this.categories = result.data
        } else {
          ElMessage.error(result.message || '加载分类列表失败')
        }
      } catch (error) {
        console.error('加载分类列表失败:', error)
        ElMessage.error('加载分类列表失败')
      } finally {
        this.loading = false
      }
    },

    showCreateDialog() {
      this.isEdit = false
      this.categoryForm = {
        name: '',
        description: ''
      }
      this.dialogVisible = true
    },

    editCategory(category) {
      this.isEdit = true
      this.categoryForm = { ...category }
      this.dialogVisible = true
    },

    async saveCategory() {
      try {
        await this.$refs.categoryForm.validate()
        this.saving = true

        const url = this.isEdit ? `/api/categories/${this.categoryForm.id}` : '/api/categories'
        const method = this.isEdit ? 'PUT' : 'POST'

        const response = await fetch(url, {
          method,
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(this.categoryForm)
        })

        const result = await response.json()
        if (result.code === 200) {
          ElMessage.success(this.isEdit ? '分类更新成功' : '分类创建成功')
          this.dialogVisible = false
          this.loadCategories()
        } else {
          ElMessage.error(result.message || '操作失败')
        }
      } catch (error) {
        console.error('保存分类失败:', error)
        ElMessage.error('操作失败')
      } finally {
        this.saving = false
      }
    },

    async deleteCategory(category) {
      try {
        await ElMessageBox.confirm(`确定要删除分类 "${category.name}" 吗？`, '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await fetch(`/api/categories/${category.id}`, {
          method: 'DELETE'
        })
        const result = await response.json()
        if (result.code === 200) {
          ElMessage.success('分类删除成功')
          this.loadCategories()
        } else {
          ElMessage.error(result.message || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除分类失败:', error)
          ElMessage.error('删除失败')
        }
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.category-manage-page {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}
</style>
