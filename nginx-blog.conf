server {
    listen 80;
    # listen 443 ssl http2;
    #listen 443 quic reuseport;
    server_name dongdong.vip www.dongdong.vip;

    if ($time_iso8601 ~ "^(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})")
    {
        set $year $1;
        set $month $2;
        set $day $3;
        set $hour $4;
        set $minutes $5;
        set $seconds $6;
    }
    access_log /nginx/logs/${year}${month}${day}-${server_name}.log;

    # ssl_certificate /etc/letsencrypt/live/shamiu.com/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/shamiu.com/privkey.pem;
    # ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
    # add_header Alt-Svc 'h3=":443"; ma=86400, h3-29=":443"; ma=86400';

    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $remote_addr;
    proxy_set_header Host $host;
    proxy_http_version 1.1;
    proxy_set_header Connection "keep-alive";

    # API代理到后端
    location /api/ {
        proxy_pass http://localhost:8080/blog/api/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $http_host;
        proxy_redirect off;
        
        # 处理跨域
        add_header Access-Control-Allow-Origin *;
        add_header Access-Control-Allow-Methods 'GET, POST, PUT, DELETE, OPTIONS';
        add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        
        # 处理OPTIONS预检请求
        if ($request_method = 'OPTIONS') {
            return 204;
        }
    }

    # 静态文件上传路径代理
    location /uploads/ {
        proxy_pass http://localhost:8080/blog/uploads/;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_set_header Host $http_host;
    }

    # 前端静态文件
    location / {
        root /data/frontend/dist/;
        index index.html;
        try_files $uri $uri/ /index.html;
        
        # 静态资源缓存
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
}
